'use client';

import { StrictMode } from 'react';
import VPSAdminChat from '@/components/VPSAdminChat';
import { useTheme } from '@/hooks/useTheme';

// Theme wrapper component to handle dark mode
const ThemeWrapper = () => {
  // Initialize theme hook to apply dark class to document root
  useTheme();

  return (
    <div className="flex items-center justify-center h-screen lg:min-h-screen bg-theme-secondary p-0 lg:p-4 overflow-hidden lg:overflow-auto transition-all duration-500">
      <VPSAdminChat />
    </div>
  );
};

export default function Home() {
  return (
    <StrictMode>
      <ThemeWrapper />
    </StrictMode>
  );
}
